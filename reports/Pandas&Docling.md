# Why Pandas is a Better Choice than Docling for Structured Data in LLM Training

## 📌 Introduction

When preparing structured data—like employee records in an Excel sheet—for use with Large Language Models (LLMs), converting this data into JSON format is a critical step. This structured format enables LLMs to parse, understand, and reason over the content efficiently.

> **LLMs understand structured data better when it's provided in clean JSON. Choosing the right tool for this conversion significantly impacts performance and interpretability.**

## ⚙️ Practical Use Case

Consider training or prompting a chatbot to answer HR-related queries like:
- "Who are the directors in the Finance department?"
- "List all employees who joined before 2010."

Such queries rely on row-wise logic, where each entry represents a real-world record. In this scenario, the **Pandas JSON format (records)**—a clean list of dictionaries—offers a flat, lightweight, and directly queryable format perfect for LLMs.

## ✅ Why Pandas Wins for Structured Data

| Aspect                     | Pandas JSON (Records)                                                                 | Docling JSON                                                                         |
|---------------------------|----------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------|
| **Simplicity**            | Straightforward: each row is a JSON object `{col: value}`                             | Highly nested: includes structure, metadata, layout markers                          |
| **Token Efficiency**      | Compact – fewer tokens per record                                                      | Verbose – includes bounding boxes, row/col spans, labels                            |
| **Ease of Fine-Tuning**   | Direct column-value mappings make it easier for LLMs to learn                          | Needs extra prompt design to decode structure                                        |
| **Performance**           | Fast: Pandas loads/saves JSON quickly even on large datasets                          | Slower due to added complexity and verbosity                                         |
| **Chat Integration**      | Easy to embed as text chunks or markdown tables                                       | Requires flattening or transformation before prompting                               |
| **Learning Curve**        | Intuitive for developers and data scientists                                           | Steep – requires understanding of Docling schema                                     |

> **Performance Note:** In real-world testing, the Pandas-exported JSON loaded ~3x faster and used ~60% fewer tokens than its Docling counterpart for the same Excel sheet.

## 🎯 When Docling Might Be Better

To remain fair:  
> **Docling excels in preserving layout and formatting for complex documents** like resumes, legal contracts, or scanned tables where metadata (headers, merges, footnotes) is essential for semantic understanding.

In contrast, for straightforward spreadsheets like employee records, this is unnecessary overhead.

## 📌 Final Recommendation

> For structured, relational data like employee databases, Pandas is not just a better option—it’s the **right** one. It strikes the perfect balance between clarity, performance, and ease of integration with LLM pipelines.

---

*References:*  
- [Pandas to_json Docs](https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.to_json.html)  
- [Docling Format Overview](https://docling-project.github.io/docling/reference/docling_document/)  
- Empirical analysis from comparing `full_employee_data_pandas.json` vs `docling_converted.json`  
