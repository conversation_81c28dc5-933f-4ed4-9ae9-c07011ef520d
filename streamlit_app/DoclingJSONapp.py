import streamlit as st
import tempfile
import json
from docling.document_converter import DocumentConverter

# ————————————————————————————————
# 1. Streamlit page configuration
# ————————————————————————————————
st.set_page_config(page_title="XLSX → JSON (Docling)", layout="centered")
st.title("Convert XLSX to JSON using Docling")

st.markdown(
    """
Upload an Excel (.xlsx) file.  
Docling will parse the spreadsheet (tables, cell text, metadata, etc.) and return a structured Document.  
Then we’ll emit that full Docling structure as plain JSON, ready for any LLM or downstream system to consume.  
"""
)

# ————————————————————————————————
# 2. File uploader (restrict to .xlsx)
# ————————————————————————————————
uploaded_file = st.file_uploader(
    "📂 Upload an XLSX file", type=["xlsx"], accept_multiple_files=False
)

if uploaded_file is not None:
    # Show a spinner while we hand off to Docling
    with st.spinner("⏳ Docling is parsing your Excel…"):
        # ————————————————————————————————
        # 3. Write the uploaded XLSX bytes to a temporary file
        #    (Docling expects a filesystem path)
        # ————————————————————————————————
        tmp = tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx")
        tmp.write(uploaded_file.read())
        tmp_path = tmp.name

        try:
            # ————————————————————————————————
            # 4. Use Docling’s DocumentConverter to convert that .xlsx
            # ————————————————————————————————
            converter = DocumentConverter()
            # The "source" argument can be a local file path (tmp_path),
            # or even a URL if you wanted to convert from an online doc.
            conversion_result = converter.convert(tmp_path)

            # The result is an object with a `.document` property.
            # That `.document` is a Docling Document (with nested pages, tables, etc.)
            doc = conversion_result.document

            # ————————————————————————————————
            # 5. Turn the Docling Document into a plain Python dict (JSON-friendly)
            # ————————————————————————————————
            #
            # Docling’s Document object exposes an `export_to_dict()` method
            # that returns the entire internal structure as nested lists/dicts.
            # (If your version of Docling does not have .export_to_dict(),
            #  check for similar methods like `.to_dict()` or `.export_to_json()`.)
            #
            doc_dict = doc.export_to_dict()

            # Serialize to a pretty JSON string
            json_str = json.dumps(doc_dict, indent=2)

            st.success("✅ XLSX parsed via Docling! You can download the JSON below.")

            # ————————————————————————————————
            # 6. Give the user a Download button for the JSON
            # ————————————————————————————————
            st.download_button(
                label="📥 Download Docling-JSON",
                data=json_str,
                file_name="docling_converted.json",
                mime="application/json",
            )

        except Exception as e:
            # If Docling fails for any reason, show the error
            st.error(f"❌ Docling raised an exception:\n```\n{e}\n```")
