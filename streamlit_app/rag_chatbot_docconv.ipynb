{"cells": [{"cell_type": "markdown", "id": "065042b5", "metadata": {}, "source": ["# RAG Chatbot Streamlit App (DocumentConverter)\n", "This notebook contains a complete Streamlit application that:\n", "1. Allows you to upload an Excel (.xlsx) file.\n", "2. Parses it using <PERSON><PERSON>'s `DocumentConverter` into structured JSON.\n", "3. Extracts paragraph text into `context_blocks`.\n", "4. Defines a simple keyword-based retrieval.\n", "5. Sends a prompt (with retrieved context) to <PERSON><PERSON> via Ollama.\n", "6. Displays the generated answer.\n", "\n", "To run this app, save the notebook and run:\n", "```\n", "streamlit run rag_chatbot_docconv.ipynb\n", "```\n", "Ensure you have installed:\n", "- `git+https://github.com/docling-ai/docling.git`\n", "- `openpyxl`\n", "- `streamlit`\n", "- `ollama` with the `qwen:7b` model pulled (`ollama pull qwen:7b`)."]}, {"cell_type": "code", "execution_count": null, "id": "3ab6eb4e", "metadata": {}, "outputs": [], "source": ["# Cell 1: Imports and Configuration\n", "import streamlit as st\n", "import subprocess\n", "import tempfile\n", "import json\n", "from docling.document_converter import DocumentConverter\n", "\n", "# Streamlit page configuration\n", "st.set_page_config(\n", "    page_title=\"<PERSON><PERSON> Chatbot (Docling + <PERSON>wen)\",\n", "    layout=\"centered\"\n", ")\n", "st.title(\"📘 RAG Chatbot using Excel (Docling DocumentConverter) + <PERSON><PERSON> (Ollama)\")\n", "\n", "st.markdown(\n", "    \"\"\"\n", "Upload an Excel (`.xlsx`) file.  \n", "<PERSON><PERSON>'s `DocumentConverter` will parse it into structured paragraphs.  \n", "Then we’ll extract text blocks for simple retrieval and feed them (plus your question) into Qwen via Ollama.\n", "    \"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": 1, "id": "2a7b670f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/Users/<USER>/Desktop/M0H51N/Computers/ETG_Chatbot/venv/bin/python\n"]}], "source": ["import sys\n", "print(sys.executable)\n"]}, {"cell_type": "code", "execution_count": null, "id": "8a2ae58b", "metadata": {}, "outputs": [], "source": ["# Cell 2: File Uploader and Docling Conversion\n", "uploaded_file = st.file_uploader(\n", "    \"📂 Upload an XLSX file\", type=[\"xlsx\"], accept_multiple_files=False\n", ")\n", "\n", "# Initialize context_blocks variable outside\n", "context_blocks = []\n", "\n", "if uploaded_file is not None:\n", "    with st.spinner(\"⏳ Parsing Excel with Docling DocumentConverter…\"):\n", "        tmp = tempfile.NamedTemporaryFile(delete=False, suffix=\".xlsx\")\n", "        tmp.write(uploaded_file.read())\n", "        tmp_path = tmp.name\n", "\n", "        try:\n", "            converter = DocumentConverter()\n", "            conversion_result = converter.convert(tmp_path)\n", "            doc = conversion_result.document\n", "            doc_dict = doc.export_to_dict()\n", "            documents = [doc_dict]\n", "            st.success(f\"✅ Parsed {len(documents)} document(s).\")\n", "        except Exception as e:\n", "            st.error(f\"❌ Docling DocumentConverter failed to parse:\\n```\\n{e}\\n```\")\n", "            st.stop()\n", "\n", "    # Build context blocks\n", "    for document in documents:\n", "        for para in document.get(\"paragraphs\", []):\n", "            text = para.get(\"text\", \"\").strip()\n", "            if text:\n", "                context_blocks.append(text)\n", "\n", "    st.success(f\"Extracted {len(context_blocks)} text blocks for retrieval.\")\n", "    if st.checkbox(\"Show sample context blocks\"):\n", "        for i, blk in enumerate(context_blocks[:5], start=1):\n", "            truncated = blk[:150] + (\"…\" if len(blk) > 150 else \"\")\n", "            st.write(f\"• Block {i}: {truncated}\")"]}, {"cell_type": "code", "execution_count": null, "id": "428218f8", "metadata": {}, "outputs": [], "source": ["# Cell 3: Retrieval and Qwen Functions\n", "if context_blocks:\n", "    def retrieve_context(query: str, blocks: list[str], top_k: int = 3) -> list[str]:\n", "        matches = [b for b in blocks if query.lower() in b.lower()]\n", "        return matches[:top_k]\n", "\n", "    def query_qwen(prompt: str) -> str:\n", "        proc = subprocess.run(\n", "            [\"ollama\", \"run\", \"qwen:7b\", \"--prompt\", prompt],\n", "            capture_output=True,\n", "            text=True,\n", "        )\n", "        return proc.stdout.strip()\n", "\n", "    st.write(\"Retrieval and Qwen functions are defined.\")"]}, {"cell_type": "code", "execution_count": null, "id": "4e8eaad6", "metadata": {}, "outputs": [], "source": ["# Cell 4: User Question Input and RAG Logic\n", "if context_blocks:\n", "    user_input = st.text_input(\"🤖 Ask a question based on the Excel content:\", value=\"\")\n", "\n", "    if user_input:\n", "        with st.spinner(\"🔍 Retrieving relevant context…\"):\n", "            top_blocks = retrieve_context(user_input, context_blocks, top_k=3)\n", "\n", "        if not top_blocks:\n", "            st.warning(\"No matching context found. Try a different keyword.\")\n", "        else:\n", "            full_prompt = (\n", "                \"You are a helpful assistant. Use the following context to answer:\\n\\n\"\n", "                + \"\\n\\n\".join(top_blocks)\n", "                + f\"\\n\\nQuestion: {user_input}\\nAnswer:\"\n", "            )\n", "\n", "            if st.checkbox(\"Show the exact prompt to <PERSON><PERSON>\"):\n", "                st.text_area(\"Full Prompt:\", full_prompt, height=200)\n", "\n", "            with st.spinner(\"⚙️ Generating answer via <PERSON><PERSON>…\"):\n", "                answer = query_qwen(full_prompt)\n", "\n", "            st.markdown(\"**Answer:**\")\n", "            st.write(answer)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}