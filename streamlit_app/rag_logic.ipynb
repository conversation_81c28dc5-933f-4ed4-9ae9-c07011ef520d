{"cells": [{"cell_type": "markdown", "id": "9cff8dee", "metadata": {}, "source": ["# rag_logic.ipynb\n", "This notebook defines the core RAG logic:\n", "1. Converting an Excel file to context blocks via Docling DocumentConverter.\n", "2. Retrieval function to get top-k matching blocks.\n", "3. Query function to call Qwen model via Ollama.\n", "4. `rag_chatbot` function that ties everything together."]}, {"cell_type": "code", "execution_count": null, "id": "dffb643b", "metadata": {}, "outputs": [], "source": ["from docling.document_converter import DocumentConverter\n", "import subprocess\n", "\n", "# Function: convert XLSX (uploaded path) to a list of context blocks\n", "def convert_xlsx_to_blocks(xlsx_path):\n", "    converter = DocumentConverter()\n", "    conversion_result = converter.convert(xlsx_path)\n", "    doc = conversion_result.document\n", "    doc_dict = doc.export_to_dict()\n", "    # Extract paragraphs as blocks\n", "    context_blocks = []\n", "    for para in doc_dict.get(\"paragraphs\", []):\n", "        text = para.get(\"text\", \"\").strip()\n", "        if text:\n", "            context_blocks.append(text)\n", "    return context_blocks\n"]}, {"cell_type": "code", "execution_count": 1, "id": "29402ab0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/Users/<USER>/Desktop/M0H51N/Computers/ETG_Chatbot/venv/bin/python\n"]}], "source": ["import sys\n", "print(sys.executable)\n"]}, {"cell_type": "code", "execution_count": null, "id": "ab47f80b", "metadata": {}, "outputs": [], "source": ["# Function: retrieve top-k matching blocks\n", "def retrieve_context(query, blocks, top_k=3):\n", "    matches = [b for b in blocks if query.lower() in b.lower()]\n", "    return matches[:top_k]\n"]}, {"cell_type": "code", "execution_count": null, "id": "21baab17", "metadata": {}, "outputs": [], "source": ["# Function: query <PERSON><PERSON> via Ollama\n", "def query_qwen(prompt):\n", "    result = subprocess.run(\n", "        [\"ollama\", \"run\", \"qwen3:1.7b\", \"--prompt\", prompt],\n", "        capture_output=True, text=True\n", "    )\n", "    return result.stdout.strip()\n"]}, {"cell_type": "code", "execution_count": null, "id": "3c1aff40", "metadata": {}, "outputs": [], "source": ["# Main RAG chatbot function: given user input and blocks, return answer\n", "def rag_chatbot(user_input, context_blocks):\n", "    top_blocks = retrieve_context(user_input, context_blocks, top_k=3)\n", "    prompt = (\n", "        \"You are a helpful assistant. Use the following context to answer:\\n\\n\" +\n", "        \"\\n\\n\".join(top_blocks) +\n", "        f\"\\n\\nQuestion: {user_input}\\nAnswer:\"\n", "    )\n", "    return query_qwen(prompt)\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}