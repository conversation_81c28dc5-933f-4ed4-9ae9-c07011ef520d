import streamlit as st
import pandas as pd
import json

# --- Custom CSS for styling ---
st.markdown("""
    <style>
        .stDownloadButton {
            font-size: 1.1em !important;
            font-weight: 600 !important;
            border-radius: 8px !important;
            margin-bottom: 0.5em !important;
        }
        .stDataFrame {
            background-color: white;
            border-radius: 8px;
        }
        .block-container {
            padding-top: 1.5rem;
            padding-bottom: 1.5rem;
        }
    </style>
""", unsafe_allow_html=True)

st.title("Employee Data Viewer")

# --- Load Excel file (fixed path) ---
file_path = "data/EmployeeSampleData/Employee Sample Data.xlsx"
df = pd.read_excel(file_path)

# --- Prepare metadata ---
salary_col = None
for col in ['Annual Salary', 'Salary', 'Base Salary']:
    if col in df.columns:
        salary_col = col
        break

metadata = {
    "total_employees": int(len(df)),
    "departments": df['Department'].dropna().unique().tolist() if 'Department' in df.columns else [],
    "countries": df['Country'].dropna().unique().tolist() if 'Country' in df.columns else [],
    "job_titles": df['Job Title'].dropna().unique().tolist() if 'Job Title' in df.columns else [],
    "salary_range": {
        "min": int(df[salary_col].min()) if salary_col else None,
        "max": int(df[salary_col].max()) if salary_col else None
    },
    "gender_distribution": df['Gender'].value_counts().to_dict() if 'Gender' in df.columns else {},
    "employment_status_counts": {
        "working": int(df['Exit Date'].isna().sum()) if 'Exit Date' in df.columns else None,
        "left": int(df['Exit Date'].notna().sum()) if 'Exit Date' in df.columns else None
    }
}
metadata_json = json.dumps(metadata, indent=2).encode('utf-8')

# --- Centered main buttons ---
left, center, right = st.columns([1, 2, 1])
with center:
    btn_col1, btn_col2 = st.columns([2, 1])
    with btn_col1:
        st.download_button(
            "📥 Download Full Dataset (JSON)",
            df.to_json(orient='records', date_format='iso').encode('utf-8'),
            "full_employee_data.json",
            "application/json",
            use_container_width=True,
            key="full_json_top"
        )
    with btn_col2:
        st.download_button(
            "🗂️ Download Metadata (JSON)",
            metadata_json,
            "employee_metadata.json",
            "application/json",
            use_container_width=True,
            key="metadata_json"
        )

# --- SIDEBAR FILTERS ---
st.sidebar.header('Filter Employee Data')
filtered_df = df.copy()

# Department filter
if 'Department' in df.columns:
    departments = df['Department'].dropna().unique().tolist()
    selected_departments = st.sidebar.multiselect('Department', departments, default=departments)
    filtered_df = filtered_df[filtered_df['Department'].isin(selected_departments)]

# Country filter
if 'Country' in df.columns:
    countries = df['Country'].dropna().unique().tolist()
    selected_countries = st.sidebar.multiselect('Country', countries, default=countries)
    filtered_df = filtered_df[filtered_df['Country'].isin(selected_countries)]

# Job Title filter
if 'Job Title' in df.columns:
    job_titles = df['Job Title'].dropna().unique().tolist()
    selected_jobs = st.sidebar.multiselect('Job Title', job_titles, default=job_titles)
    filtered_df = filtered_df[filtered_df['Job Title'].isin(selected_jobs)]

# Gender filter
if 'Gender' in df.columns:
    genders = df['Gender'].dropna().unique().tolist()
    selected_genders = st.sidebar.multiselect('Gender', genders, default=genders)
    filtered_df = filtered_df[filtered_df['Gender'].isin(selected_genders)]

# Age filter
if 'Age' in df.columns:
    min_age = int(df['Age'].min())
    max_age = int(df['Age'].max())
    age_range = st.sidebar.slider('Age', min_age, max_age, (min_age, max_age))
    filtered_df = filtered_df[(filtered_df['Age'] >= age_range[0]) & (filtered_df['Age'] <= age_range[1])]

# Salary Range filter
if salary_col:
    min_salary = int(df[salary_col].min())
    max_salary = int(df[salary_col].max())
    salary_range = st.sidebar.slider('Salary Range', min_salary, max_salary, (min_salary, max_salary), step=1000)
    filtered_df = filtered_df[(filtered_df[salary_col] >= salary_range[0]) & (filtered_df[salary_col] <= salary_range[1])]

# Working or Left Employees filter
if 'Exit Date' in df.columns:
    emp_status = st.sidebar.selectbox('Employment Status', ['All', 'Working', 'Left'])
    if emp_status == 'Working':
        filtered_df = filtered_df[filtered_df['Exit Date'].isna()]
    elif emp_status == 'Left':
        filtered_df = filtered_df[filtered_df['Exit Date'].notna()]

# --- SHOW FILTERED DATA ---
st.write(filtered_df)

# --- EXPORT FILTERED DATA AS JSON ---
st.markdown("### 🤖 Export Filtered Data as LLM-Readable JSON")
filtered_json = filtered_df.to_json(orient='records', date_format='iso').encode('utf-8')
st.download_button(
    "📥 Download Filtered Data (JSON)",
    filtered_json,
    "filtered_employee_data.json",
    "application/json"
)
