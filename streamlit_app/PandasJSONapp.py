import streamlit as st
import pandas as pd
import json

# --- Custom CSS for styling ---
st.markdown(
    """
    <style>
        /* Container styling */
        .main .block-container {
            background-color: #f5f5f5;
            padding: 2.5rem 2rem;
            border-radius: 12px;
        }

        /* Title styling */
        .stApp > .main > .block-container > h1 {
            font-size: 3rem !important;
            color: #2c3e50 !important;
            font-weight: 700 !important;
            margin-bottom: 1.5rem !important;
            text-align: center;
        }

        /* DataFrame container */
        .stDataFrame,
        .element-container .stAgGrid {
            background-color: #ffffff !important;
            border-radius: 8px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 0.5rem;
        }

        /* Download button styling */
        .stDownloadButton > button {
            background-color: #1f77b4 !important;
            color: #ffffff !important;
            font-size: 1.15em !important;
            font-weight: 600 !important;
            padding: 0.75rem 1rem !important;
            border-radius: 8px !important;
            border: none !important;
            width: 100% !important;
            transition: background-color 0.2s ease-in-out;
            box-shadow: none !important;
        }
        .stDownloadButton > button:hover {
            background-color: #155a8a !important;
        }

        /* Button spacing */
        .stButton,
        .stDownloadButton {
            margin: 1rem 0;
        }
    </style>
    """,
    unsafe_allow_html=True
)

st.title("Employee Data Viewer")

# --- Load data ---
file_path = "data/EmployeeSampleData/Employee Sample Data.xlsx"
df = pd.read_excel(file_path)

# --- Prepare metadata ---
salary_col = next(
    (col for col in ["Annual Salary", "Salary", "Base Salary"] if col in df.columns),
    None
)

metadata = {
    "total_employees": int(len(df)),
    "departments": df["Department"].dropna().unique().tolist() if "Department" in df.columns else [],
    "countries": df["Country"].dropna().unique().tolist() if "Country" in df.columns else [],
    "job_titles": df["Job Title"].dropna().unique().tolist() if "Job Title" in df.columns else [],
    "salary_range": {
        "min": int(df[salary_col].min()) if salary_col else None,
        "max": int(df[salary_col].max()) if salary_col else None
    },
    "gender_distribution": df["Gender"].value_counts().to_dict() if "Gender" in df.columns else {},
    "employment_status_counts": {
        "working": int(df["Exit Date"].isna().sum()) if "Exit Date" in df.columns else None,
        "left": int(df["Exit Date"].notna().sum()) if "Exit Date" in df.columns else None
    }
}

metadata_json = json.dumps(metadata, indent=2).encode("utf-8")

# --- Display data table ---
st.dataframe(df, use_container_width=True)

# --- Download buttons in two columns ---
col1, col2 = st.columns(2)
with col1:
    st.download_button(
        label="📥 Download Full Dataset (JSON)",
        data=df.to_json(orient="records", date_format="iso").encode("utf-8"),
        file_name="full_employee_data.json",
        mime="application/json"
    )
with col2:
    st.download_button(
        label="🗂️ Download Metadata (JSON)",
        data=metadata_json,
        file_name="employee_metadata.json",
        mime="application/json"
    )
