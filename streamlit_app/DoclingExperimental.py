import streamlit as st
import tempfile
import json
from docling.document_converter import DocumentConverter

# For formula vs. value extraction
import openpyxl


st.set_page_config(page_title="Docling XLSX Features", layout="centered")
st.title("Docling XLSX: JSON / Markdown / HTML + Formulas")

st.markdown(
    """
Upload an Excel file to explore:
- Docling JSON (full document structure)
- Docling-generated Markdown
- Docling-generated HTML
- Raw cell formulas vs. computed values (via Openpyxl)
"""
)

uploaded = st.file_uploader("Upload .xlsx", type=["xlsx"])
if uploaded:
    with st.spinner("Parsing…"):
        # Save upload to temp file
        tmp = tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx")
        tmp.write(uploaded.read())
        tmp_path = tmp.name

        # 1) Docling conversion
        converter = DocumentConverter()
        result = converter.convert(tmp_path)
        doc = result.document

        # Prepare each format
        try:
            doc_json = doc.export_to_dict()
        except AttributeError:
            doc_json = None

        try:
            doc_md = doc.export_to_markdown()
        except AttributeError:
            doc_md = None

        try:
            doc_html = doc.export_to_html()
        except AttributeError:
            doc_html = None

        # 2) Openpyxl: extract formulas vs. values
        wb_values = openpyxl.load_workbook(tmp_path, data_only=True)
        wb_formulas = openpyxl.load_workbook(tmp_path, data_only=False)
        sheets = wb_values.sheetnames

    # Sidebar: choose which output to view/download
    mode = st.sidebar.selectbox("Choose output", ["Docling JSON", "Markdown", "HTML", "Formulas"])

    if mode == "Docling JSON":
        if doc_json is None:
            st.error("Docling JSON export not available.")
        else:
            json_str = json.dumps(doc_json, indent=2)
            st.download_button("Download JSON", data=json_str, file_name="docling_full.json", mime="application/json")
            st.code(json_str[:1000] + ("\n…\n" if len(json_str) > 1000 else ""))

    elif mode == "Markdown":
        if doc_md is None:
            st.error("Docling Markdown export not available.")
        else:
            st.download_button("Download Markdown", data=doc_md, file_name="docling_export.md", mime="text/markdown")
            st.text_area("Markdown preview", doc_md, height=400)

    elif mode == "HTML":
        if doc_html is None:
            st.error("Docling HTML export not available.")
        else:
            st.download_button("Download HTML", data=doc_html, file_name="docling_export.html", mime="text/html")
            st.components.v1.html(doc_html, height=500, scrolling=True)

    else:  # Formulas vs Values
        sheet = st.sidebar.selectbox("Select sheet", sheets)
        ws_val = wb_values[sheet]
        ws_fm = wb_formulas[sheet]

        rows = []
        for row in ws_val.iter_rows():
            for cell in row:
                coord = cell.coordinate
                value = cell.value
                formula = ws_fm[coord].value if ws_fm[coord].data_type == "f" else None
                rows.append({"cell": coord, "formula": formula, "value": value})

        st.download_button(
            "Download formulas.json",
            data=json.dumps(rows, indent=2),
            file_name=f"{sheet}_formulas.json",
            mime="application/json",
        )

        # Show in table form (first 100 cells)
        import pandas as pd
        df = pd.DataFrame(rows)
        st.dataframe(df.head(100))
