# rag_logic.py

import os
import subprocess
from docling.document_converter import DocumentConverter
from sentence_transformers import SentenceTransformer
import numpy as np
import streamlit as st

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
XLSX_PATH = os.path.join(BASE_DIR, "data", "EmployeeSampleData", "Employee Sample Data.xlsx")

def convert_xlsx_to_blocks(xlsx_path=None):
    path = xlsx_path if xlsx_path else XLSX_PATH
    if not os.path.isfile(path):
        st.warning(f"⚠️ Could not find the Excel file at:\n  {path}")
        return []
    try:
        converter = DocumentConverter()
        conversion_result = converter.convert(path)
        doc = conversion_result.document
        doc_dict = doc.export_to_dict()
    except Exception as e:
        st.error(f"❌ Error converting Excel with Docling:\n```\n{e}\n```")
        return []
    context_blocks = []
    for para in doc_dict.get("paragraphs", []):
        text = para.get("text", "").strip()
        if text:
            context_blocks.append(text)
    if not context_blocks:
        st.warning("⚠️ Docling parsed the file but found no paragraphs to use as context blocks.")
    return context_blocks

context_blocks = convert_xlsx_to_blocks()

if context_blocks:
    try:
        embed_model = SentenceTransformer('all-MiniLM-L6-v2')
        block_embeddings = embed_model.encode(context_blocks, convert_to_numpy=True)
    except Exception as e:
        st.error(f"❌ Could not load SentenceTransformer model:\n```\n{e}\n```")
        context_blocks = []
        block_embeddings = np.zeros((0, 384), dtype=np.float32)
else:
    block_embeddings = np.zeros((0, 384), dtype=np.float32)

def retrieve_context(query, top_k=3):
    if not context_blocks:
        return []
    try:
        q_emb = embed_model.encode([query], convert_to_numpy=True)
    except Exception as e:
        st.error(f"❌ Could not embed your query:\n```\n{e}\n```")
        return []
    diffs = block_embeddings - q_emb
    dists = np.sum(np.square(diffs), axis=1)
    top_k = min(top_k, len(context_blocks))
    best_indices = np.argsort(dists)[:top_k]
    return [context_blocks[i] for i in best_indices]

def query_qwen(prompt):
    try:
        result = subprocess.run(
            ["ollama", "run", "qwen3:1.7b", "--prompt", prompt],
            capture_output=True,
            text=True,
        )
        return result.stdout.strip()
    except Exception as e:
        st.error(f"❌ Could not call Ollama/Qwen:\n```\n{e}\n```")
        return "Error: failed to generate response."

def rag_chatbot(user_input):
    top_blocks = retrieve_context(user_input, top_k=3)
    if top_blocks:
        prompt = (
            "You are a helpful assistant. Use the following context to answer:\n\n"
            + "\n\n".join(top_blocks)
            + f"\n\nQuestion: {user_input}\nAnswer:"
        )
    else:
        prompt = f"You are a helpful assistant.\nQuestion: {user_input}\nAnswer:"
    return query_qwen(prompt)
