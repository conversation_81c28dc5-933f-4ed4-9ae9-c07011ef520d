import streamlit as st
import pandas as pd
import os
from langchain_community.embeddings import OllamaEmbeddings
from langchain_community.vectorstores import Chroma
from langchain_community.chat_models import ChatOllama
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.prompts import PromptTemplate
from langchain.schema import Document
from langchain.chains import RetrievalQA

class SimpleExcelChatbot:
    def __init__(self, excel_path: str):
        self.excel_path = excel_path
        self.df = None
        self.qa_chain = None
        
    def load_data(self):
        """Simply load the Excel file"""
        try:
            df = pd.read_excel(self.excel_path)
            return df.head(1000).to_string(index=False), df
            return True
        except Exception as e:
            st.error(f"Error loading file: {e}")
            return False
    
    def setup_chatbot(self):
        """Setup the basic RAG chatbot"""
        try:
            # Create simple text from the dataframe
            text_content = f"""
Dataset Summary:
- Total rows: {len(self.df)}
- Columns: {list(self.df.columns)}

Data:
{self.df.to_string()}

Statistics:
{self.df.describe(include='all').to_string()}
            """
            
            # Create document
            doc = Document(page_content=text_content)
            
            # Split text
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=2000,
                chunk_overlap=200
            )
            texts = text_splitter.split_documents([doc])
            
            # Create embeddings
            embeddings = OllamaEmbeddings(model="nomic-embed-text")
            
            # Create vector store
            vectorstore = Chroma.from_documents(texts, embeddings)
            
            # Create LLM
            llm = ChatOllama(
                model="qwen3:1.7b",
                temperature=0
            )
            
            # Create simple prompt
            prompt = PromptTemplate(
                template="""Use the following context to answer the question. Give a direct, short answer.

Context: {context}

Question: {question}

Answer:""",
                input_variables=["context", "question"]
            )
            
            # Create QA chain
            self.qa_chain = RetrievalQA.from_chain_type(
                llm=llm,
                chain_type="stuff",
                retriever=vectorstore.as_retriever(search_kwargs={"k": 3}),
                chain_type_kwargs={"prompt": prompt}
            )
            
            return True
            
        except Exception as e:
            st.error(f"Error setting up chatbot: {e}")
            return False
    
    def ask(self, question: str) -> str:
        """Ask a question"""
        if not self.qa_chain:
            return "Chatbot not initialized"
        
        try:
            response = self.qa_chain.run(question)
            return response
        except Exception as e:
            return f"Error: {e}"

def main():
    st.title("Simple Excel Chatbot")
    
    # File path
    excel_file = "data/EmployeeSampleData/Employee Sample Data.xlsx"
    
    # Initialize chatbot
    if 'chatbot' not in st.session_state:
        st.session_state.chatbot = SimpleExcelChatbot(excel_file)
        st.session_state.ready = False
    
    # Check if file exists
    if not os.path.exists(excel_file):
        st.error(f"File not found: {excel_file}")
        st.stop()
    
    # Setup button
    if not st.session_state.ready:
        if st.button("Setup Chatbot"):
            with st.spinner("Loading data..."):
                if st.session_state.chatbot.load_data():
                    st.success("Data loaded!")
                    
            with st.spinner("Setting up AI..."):
                if st.session_state.chatbot.setup_chatbot():
                    st.success("Chatbot ready!")
                    st.session_state.ready = True
                    st.rerun()
    
    # Chat interface
    if st.session_state.ready:
        st.subheader("Ask Questions")
        
        # Show data info
        df = st.session_state.chatbot.df
        st.info(f"Dataset: {len(df)} rows, {len(df.columns)} columns")
        
        # Question input
        question = st.text_input("Your question:")
        
        if st.button("Ask") and question:
            with st.spinner("Getting answer..."):
                answer = st.session_state.chatbot.ask(question)
                st.write("**Answer:**", answer)
        
        # Sample questions
        st.subheader("Try these:")
        sample_questions = [
            "How many employees are there?",
            "How many employees have exited?",
            "What are the column names?",
            "What departments exist?"
        ]
        
        for q in sample_questions:
            if st.button(q):
                with st.spinner("Getting answer..."):
                    answer = st.session_state.chatbot.ask(q)
                    st.write("**Answer:**", answer)

if __name__ == "__main__":
    main()