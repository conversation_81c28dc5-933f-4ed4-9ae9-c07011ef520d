import streamlit as st
import pandas as pd
import os
from langchain_community.embeddings import OllamaEmbeddings
from langchain_community.vectorstores import Chroma
from langchain_community.chat_models import ChatOllama
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.prompts import PromptTemplate
from langchain.schema import Document
from langchain.chains import RetrievalQA

class SimpleExcelChatbot:
    def __init__(self, excel_path: str):
        self.excel_path = excel_path
        self.df = None
        self.qa_chain = None
        
    def load_data(self):
        """Simply load the Excel file"""
        try:
            self.df = pd.read_excel(self.excel_path)
            return True
        except Exception as e:
            st.error(f"Error loading file: {e}")
            return False
    
    def setup_chatbot(self):
        """Setup the basic RAG chatbot"""
        try:
            # Create better structured text content for tabular data
            documents = []

            # 1. Dataset overview
            overview = f"""
DATASET OVERVIEW:
- Total rows: {len(self.df)}
- Total columns: {len(self.df.columns)}
- Column names: {', '.join(self.df.columns)}

COLUMN DETAILS:
"""
            for col in self.df.columns:
                col_info = f"- {col}: {self.df[col].dtype}"
                if self.df[col].dtype == 'object':
                    unique_vals = self.df[col].dropna().unique()
                    if len(unique_vals) <= 10:
                        col_info += f" (values: {', '.join(map(str, unique_vals))})"
                    else:
                        col_info += f" ({len(unique_vals)} unique values)"
                elif self.df[col].dtype in ['int64', 'float64']:
                    col_info += f" (range: {self.df[col].min()} to {self.df[col].max()})"
                overview += col_info + "\n"

            documents.append(Document(page_content=overview))

            # 2. Create multiple data chunks for better coverage
            chunk_size = 50  # rows per chunk
            for i in range(0, min(len(self.df), 200), chunk_size):  # Process up to 200 rows
                chunk_df = self.df.iloc[i:i+chunk_size]
                chunk_data = f"DATA CHUNK {i//chunk_size + 1} (Rows {i+1}-{min(i+chunk_size, len(self.df))}):\n"

                for idx, row in chunk_df.iterrows():
                    row_items = []
                    for col in self.df.columns:
                        value = row[col]
                        if pd.isna(value):
                            value = "NULL"
                        row_items.append(f"{col}={value}")
                    chunk_data += f"Row {idx + 1}: " + ", ".join(row_items) + "\n"

                documents.append(Document(page_content=chunk_data))

            # 3. Create a searchable name index for quick lookups
            if any(col.lower() in ['name', 'employee name', 'full name'] for col in self.df.columns):
                name_col = None
                for col in self.df.columns:
                    if col.lower() in ['name', 'employee name', 'full name']:
                        name_col = col
                        break

                if name_col:
                    name_index = "EMPLOYEE NAME INDEX:\n"
                    for idx, row in self.df.iterrows():
                        name = row[name_col]
                        if pd.notna(name):
                            # Include key info for each person
                            person_info = [f"Name={name}"]
                            for col in ['Age', 'Department', 'Position', 'Job Title', 'Salary']:
                                if col in self.df.columns and pd.notna(row[col]):
                                    person_info.append(f"{col}={row[col]}")
                            name_index += f"Row {idx + 1}: " + ", ".join(person_info) + "\n"

                    documents.append(Document(page_content=name_index))

            # 4. Statistics and summaries
            stats_text = "DATASET STATISTICS:\n"

            # Count statistics
            stats_text += f"Total employee count: {len(self.df)}\n"

            # Check for common employee data patterns
            if 'Exit Date' in self.df.columns:
                exited = self.df['Exit Date'].notna().sum()
                working = len(self.df) - exited
                stats_text += f"Working employees: {working}\n"
                stats_text += f"Exited employees: {exited}\n"

            if 'Department' in self.df.columns:
                dept_counts = self.df['Department'].value_counts()
                stats_text += "Department breakdown:\n"
                for dept, count in dept_counts.items():
                    stats_text += f"  - {dept}: {count} employees\n"

            if 'Position' in self.df.columns or 'Job Title' in self.df.columns:
                pos_col = 'Position' if 'Position' in self.df.columns else 'Job Title'
                pos_counts = self.df[pos_col].value_counts().head(10)
                stats_text += f"Top positions:\n"
                for pos, count in pos_counts.items():
                    stats_text += f"  - {pos}: {count} employees\n"

            documents.append(Document(page_content=stats_text))

            # Split text into smaller, more focused chunks
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=800,  # Smaller chunks for better retrieval
                chunk_overlap=100
            )
            texts = text_splitter.split_documents(documents)

            # Create embeddings
            embeddings = OllamaEmbeddings(model="nomic-embed-text")

            # Create vector store
            vectorstore = Chroma.from_documents(texts, embeddings)

            # Create LLM
            llm = ChatOllama(
                model="qwen3:1.7b",
                temperature=0
            )

            # Create better prompt for tabular data
            prompt = PromptTemplate(
                template="""You are a data analyst assistant. Answer questions about the employee dataset using the provided context.

Context: {context}

Question: {question}

Instructions:
- Give direct, concise answers
- Use specific numbers and names from the data
- Don't show your reasoning process
- If someone is not found in the data, say so clearly
- For calculations, provide the result directly

Answer:""",
                input_variables=["context", "question"]
            )

            # Create QA chain
            self.qa_chain = RetrievalQA.from_chain_type(
                llm=llm,
                chain_type="stuff",
                retriever=vectorstore.as_retriever(search_kwargs={"k": 6}),  # Get more context for better coverage
                chain_type_kwargs={"prompt": prompt}
            )

            return True

        except Exception as e:
            st.error(f"Error setting up chatbot: {e}")
            return False
    
    def analyze_data_directly(self, question: str) -> str:
        """Analyze data directly using pandas for better accuracy"""
        question_lower = question.lower()

        try:
            # Gender analysis
            if any(word in question_lower for word in ['gender', 'male', 'female', 'ratio', 'sex']):
                if 'Gender' in self.df.columns:
                    gender_counts = self.df['Gender'].value_counts()
                    male_count = gender_counts.get('Male', 0) + gender_counts.get('M', 0)
                    female_count = gender_counts.get('Female', 0) + gender_counts.get('F', 0)

                    if 'ratio' in question_lower:
                        if female_count > 0:
                            ratio = f"{male_count}:{female_count}"
                            return f"Male to female ratio: {ratio} (Males: {male_count}, Females: {female_count})"
                        else:
                            return f"Males: {male_count}, Females: {female_count}"
                    else:
                        return f"Gender breakdown - Males: {male_count}, Females: {female_count}"
                else:
                    return "No gender column found in the dataset"

            # Count questions
            if any(word in question_lower for word in ['how many', 'count', 'total']):
                if 'employee' in question_lower:
                    return f"Total employees: {len(self.df)}"
                elif 'exit' in question_lower or 'left' in question_lower:
                    if 'Exit Date' in self.df.columns:
                        exited = self.df['Exit Date'].notna().sum()
                        return f"Employees who have exited: {exited}"
                elif 'working' in question_lower or 'active' in question_lower:
                    if 'Exit Date' in self.df.columns:
                        working = self.df['Exit Date'].isna().sum()
                        return f"Currently working employees: {working}"
                elif 'department' in question_lower:
                    if 'Department' in self.df.columns:
                        dept_counts = self.df['Department'].value_counts()
                        result = "Department counts:\n"
                        for dept, count in dept_counts.items():
                            result += f"- {dept}: {count}\n"
                        return result

            # Column questions
            if 'column' in question_lower:
                return f"Columns: {', '.join(self.df.columns)}"

            # Department questions
            if 'department' in question_lower and 'exist' in question_lower:
                if 'Department' in self.df.columns:
                    departments = self.df['Department'].dropna().unique()
                    return f"Departments: {', '.join(departments)}"

            # Age-related questions
            if 'age' in question_lower:
                # Check if it's asking for specific people
                names_in_question = []
                for idx, row in self.df.iterrows():
                    name_col = None
                    for col in ['Name', 'Employee Name', 'Full Name']:
                        if col in self.df.columns:
                            name_col = col
                            break

                    if name_col and pd.notna(row[name_col]):
                        name = str(row[name_col]).lower()
                        if name in question_lower:
                            names_in_question.append((row[name_col], row.get('Age', 'Unknown')))

                if names_in_question:
                    if len(names_in_question) == 1:
                        name, age = names_in_question[0]
                        return f"{name}'s age: {age}"
                    else:
                        result = "Ages found:\n"
                        total_age = 0
                        valid_ages = 0
                        for name, age in names_in_question:
                            result += f"- {name}: {age}\n"
                            if isinstance(age, (int, float)) and not pd.isna(age):
                                total_age += age
                                valid_ages += 1

                        if valid_ages > 1 and ('+' in question_lower or 'sum' in question_lower):
                            result += f"Sum of ages: {total_age}"

                        return result

            return None  # Let RAG handle it

        except Exception as e:
            return None  # Fall back to RAG

    def ask(self, question: str) -> str:
        """Ask a question - try direct analysis first, then RAG"""
        if not self.qa_chain:
            return "Chatbot not initialized"

        # Try direct analysis first
        direct_result = self.analyze_data_directly(question)
        if direct_result:
            return direct_result

        # Fall back to RAG for complex questions
        try:
            response = self.qa_chain.run(question)
            return response
        except Exception as e:
            return f"Error: {e}"

def main():
    st.title("Simple Excel Chatbot")
    
    # File path
    excel_file = "data/EmployeeSampleData/Employee Sample Data.xlsx"
    
    # Initialize chatbot
    if 'chatbot' not in st.session_state:
        st.session_state.chatbot = SimpleExcelChatbot(excel_file)
        st.session_state.ready = False
    
    # Check if file exists
    if not os.path.exists(excel_file):
        st.error(f"File not found: {excel_file}")
        st.stop()
    
    # Setup button
    if not st.session_state.ready:
        if st.button("Setup Chatbot"):
            with st.spinner("Loading data..."):
                if st.session_state.chatbot.load_data():
                    st.success("Data loaded!")
                    
            with st.spinner("Setting up AI..."):
                if st.session_state.chatbot.setup_chatbot():
                    st.success("Chatbot ready!")
                    st.session_state.ready = True
                    st.rerun()
    
    # Chat interface
    if st.session_state.ready:
        st.subheader("Ask Questions")
        
        # Show data info
        df = st.session_state.chatbot.df
        st.info(f"Dataset: {len(df)} rows, {len(df.columns)} columns")
        
        # Question input
        question = st.text_input("Your question:")
        
        if st.button("Ask") and question:
            with st.spinner("Getting answer..."):
                answer = st.session_state.chatbot.ask(question)
                st.write("**Answer:**", answer)
        
        # Sample questions
        st.subheader("Try these:")
        sample_questions = [
            "How many employees are there?",
            "How many employees have exited?",
            "What are the column names?",
            "What departments exist?"
        ]
        
        for q in sample_questions:
            if st.button(q):
                with st.spinner("Getting answer..."):
                    answer = st.session_state.chatbot.ask(q)
                    st.write("**Answer:**", answer)

if __name__ == "__main__":
    main()