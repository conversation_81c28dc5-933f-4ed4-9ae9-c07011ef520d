import streamlit as st
import pandas as pd
import os
from langchain_community.embeddings import OllamaEmbeddings
from langchain_community.vectorstores import Chroma
from langchain_community.chat_models import ChatOllama
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.prompts import PromptTemplate
from langchain.schema import Document
from langchain.chains import RetrievalQA

class SimpleExcelChatbot:
    def __init__(self, excel_path: str):
        self.excel_path = excel_path
        self.df = None
        self.qa_chain = None
        
    def load_data(self):
        """Simply load the Excel file"""
        try:
            self.df = pd.read_excel(self.excel_path)
            return True
        except Exception as e:
            st.error(f"Error loading file: {e}")
            return False
    
    def setup_chatbot(self):
        """Setup the basic RAG chatbot"""
        try:
            # Create better structured text content for tabular data
            documents = []

            # 1. Dataset overview
            overview = f"""
DATASET OVERVIEW:
- Total rows: {len(self.df)}
- Total columns: {len(self.df.columns)}
- Column names: {', '.join(self.df.columns)}

COLUMN DETAILS:
"""
            for col in self.df.columns:
                col_info = f"- {col}: {self.df[col].dtype}"
                if self.df[col].dtype == 'object':
                    unique_vals = self.df[col].dropna().unique()
                    if len(unique_vals) <= 10:
                        col_info += f" (values: {', '.join(map(str, unique_vals))})"
                    else:
                        col_info += f" ({len(unique_vals)} unique values)"
                elif self.df[col].dtype in ['int64', 'float64']:
                    col_info += f" (range: {self.df[col].min()} to {self.df[col].max()})"
                overview += col_info + "\n"

            documents.append(Document(page_content=overview))

            # 2. Sample data rows (first 20 rows)
            sample_data = "SAMPLE DATA (First 20 rows):\n"
            for idx, row in self.df.head(20).iterrows():
                row_text = f"Row {idx + 1}: "
                row_items = []
                for col in self.df.columns:
                    value = row[col]
                    if pd.isna(value):
                        value = "NULL"
                    row_items.append(f"{col}={value}")
                sample_data += row_text + ", ".join(row_items) + "\n"

            documents.append(Document(page_content=sample_data))

            # 3. Statistics and summaries
            stats_text = "DATASET STATISTICS:\n"

            # Count statistics
            stats_text += f"Total employee count: {len(self.df)}\n"

            # Check for common employee data patterns
            if 'Exit Date' in self.df.columns:
                exited = self.df['Exit Date'].notna().sum()
                working = len(self.df) - exited
                stats_text += f"Working employees: {working}\n"
                stats_text += f"Exited employees: {exited}\n"

            if 'Department' in self.df.columns:
                dept_counts = self.df['Department'].value_counts()
                stats_text += "Department breakdown:\n"
                for dept, count in dept_counts.items():
                    stats_text += f"  - {dept}: {count} employees\n"

            if 'Position' in self.df.columns or 'Job Title' in self.df.columns:
                pos_col = 'Position' if 'Position' in self.df.columns else 'Job Title'
                pos_counts = self.df[pos_col].value_counts().head(10)
                stats_text += f"Top positions:\n"
                for pos, count in pos_counts.items():
                    stats_text += f"  - {pos}: {count} employees\n"

            documents.append(Document(page_content=stats_text))

            # Split text into smaller, more focused chunks
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=800,  # Smaller chunks for better retrieval
                chunk_overlap=100
            )
            texts = text_splitter.split_documents(documents)

            # Create embeddings
            embeddings = OllamaEmbeddings(model="nomic-embed-text")

            # Create vector store
            vectorstore = Chroma.from_documents(texts, embeddings)

            # Create LLM
            llm = ChatOllama(
                model="qwen3:1.7b",
                temperature=0
            )

            # Create better prompt for tabular data
            prompt = PromptTemplate(
                template="""You are an expert data analyst. Use the provided context about an employee dataset to answer questions accurately.

Context: {context}

Question: {question}

Instructions:
- Give direct, specific answers based on the data
- Include numbers when asked about counts
- If asked about columns, list them clearly
- If asked about departments or positions, provide the actual names from the data

Answer:""",
                input_variables=["context", "question"]
            )

            # Create QA chain
            self.qa_chain = RetrievalQA.from_chain_type(
                llm=llm,
                chain_type="stuff",
                retriever=vectorstore.as_retriever(search_kwargs={"k": 4}),  # Get more context
                chain_type_kwargs={"prompt": prompt}
            )

            return True

        except Exception as e:
            st.error(f"Error setting up chatbot: {e}")
            return False
    
    def ask(self, question: str) -> str:
        """Ask a question"""
        if not self.qa_chain:
            return "Chatbot not initialized"
        
        try:
            response = self.qa_chain.run(question)
            return response
        except Exception as e:
            return f"Error: {e}"

def main():
    st.title("Simple Excel Chatbot")
    
    # File path
    excel_file = "data/EmployeeSampleData/Employee Sample Data.xlsx"
    
    # Initialize chatbot
    if 'chatbot' not in st.session_state:
        st.session_state.chatbot = SimpleExcelChatbot(excel_file)
        st.session_state.ready = False
    
    # Check if file exists
    if not os.path.exists(excel_file):
        st.error(f"File not found: {excel_file}")
        st.stop()
    
    # Setup button
    if not st.session_state.ready:
        if st.button("Setup Chatbot"):
            with st.spinner("Loading data..."):
                if st.session_state.chatbot.load_data():
                    st.success("Data loaded!")
                    
            with st.spinner("Setting up AI..."):
                if st.session_state.chatbot.setup_chatbot():
                    st.success("Chatbot ready!")
                    st.session_state.ready = True
                    st.rerun()
    
    # Chat interface
    if st.session_state.ready:
        st.subheader("Ask Questions")
        
        # Show data info
        df = st.session_state.chatbot.df
        st.info(f"Dataset: {len(df)} rows, {len(df.columns)} columns")
        
        # Question input
        question = st.text_input("Your question:")
        
        if st.button("Ask") and question:
            with st.spinner("Getting answer..."):
                answer = st.session_state.chatbot.ask(question)
                st.write("**Answer:**", answer)
        
        # Sample questions
        st.subheader("Try these:")
        sample_questions = [
            "How many employees are there?",
            "How many employees have exited?",
            "What are the column names?",
            "What departments exist?"
        ]
        
        for q in sample_questions:
            if st.button(q):
                with st.spinner("Getting answer..."):
                    answer = st.session_state.chatbot.ask(q)
                    st.write("**Answer:**", answer)

if __name__ == "__main__":
    main()